import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceVideoCaptureBloc extends Mock implements FaceVideoCaptureBloc {}
class MockVideoStorageRepository extends Mock implements VideoStorageRepository {}

void main() {
  group('[FACE_VERIFICATION] CameraPreviewWidget Video Path Tests', () {
    late MockFaceVideoCaptureBloc mockBloc;
    late MockVideoStorageRepository mockRepository;
    late Directory tempDir;

    setUpAll(() {
      registerFallbackValue(const InitializeCamera());
      registerFallbackValue(const VideoRecordingCompleted(videoPath: ''));
    });

    setUp(() {
      mockBloc = MockFaceVideoCaptureBloc();
      mockRepository = MockVideoStorageRepository();
      tempDir = Directory.systemTemp.createTempSync('video_path_test_');

      // Default mock behaviors
      when(() => mockBloc.state).thenReturn(const Initial());
      when(() => mockBloc.stream).thenAnswer((_) => const Stream.empty());
      when(() => mockRepository.currentVideoPath).thenReturn(null);
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<FaceVideoCaptureBloc>.value(value: mockBloc),
            RepositoryProvider<VideoStorageRepository>.value(value: mockRepository),
          ],
          child: const Scaffold(
            body: CameraPreviewWidget(),
          ),
        ),
      );
    }

    group('[FACE_VERIFICATION] Video Path Synchronization', () {
      testWidgets('should get expected video path from repository before recording',
          (tester) async {
        // Arrange
        const expectedPath = '/test/path/video.mp4';
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(
          const Recording(
            elapsedTime: Duration.zero,
            remainingTime: Duration(seconds: 9),
            config: VideoCaptureConfig(),
          ),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        verify(() => mockRepository.currentVideoPath).called(1);
      });

      testWidgets('should handle null video path from repository gracefully',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath).thenReturn(null);
        when(() => mockBloc.state).thenReturn(
          const Recording(
            elapsedTime: Duration.zero,
            remainingTime: Duration(seconds: 9),
            config: VideoCaptureConfig(),
          ),
        );

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        verify(() => mockRepository.currentVideoPath).called(1);
      });
    });

    group('[FACE_VERIFICATION] Alternative File Handling', () {
      testWidgets('should move file when paths mismatch', (tester) async {
        // Arrange
        final actualFile = File('${tempDir.path}/actual_video.mp4');
        final expectedPath = '${tempDir.path}/expected_video.mp4';
        
        // Create the actual file
        await actualFile.writeAsBytes([1, 2, 3, 4]);
        
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Simulate the widget's _handlePathMismatch method behavior
        // (This would be called internally when paths don't match)
        
        // Assert
        expect(await actualFile.exists(), isTrue);
      });

      testWidgets('should handle file move failure gracefully', (tester) async {
        // Arrange
        const actualPath = '/nonexistent/actual_video.mp4';
        const expectedPath = '/test/expected_video.mp4';
        
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
      });
    });

    group('[FACE_VERIFICATION] Video Path Builder Integration', () {
      testWidgets('should use expected path when available', (tester) async {
        // Arrange
        const expectedPath = '/test/path/video.mp4';
        when(() => mockRepository.currentVideoPath).thenReturn(expectedPath);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        verify(() => mockRepository.currentVideoPath).called(atLeast(1));
      });

      testWidgets('should generate fallback path when expected path is null',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath).thenReturn(null);
        when(() => mockBloc.state).thenReturn(const Initial());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        verify(() => mockRepository.currentVideoPath).called(atLeast(1));
      });
    });

    group('[FACE_VERIFICATION] Error Handling', () {
      testWidgets('should handle repository access errors gracefully',
          (tester) async {
        // Arrange
        when(() => mockRepository.currentVideoPath)
            .thenThrow(Exception('Repository error'));
        when(() => mockBloc.state).thenReturn(
          const Recording(
            elapsedTime: Duration.zero,
            remainingTime: Duration(seconds: 9),
            config: VideoCaptureConfig(),
          ),
        );

        // Act & Assert - Should not throw
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        verify(() => mockRepository.currentVideoPath).called(1);
      });
    });
  });
}
